import { SSMClient } from '@aws-sdk/client-ssm';
import { getError<PERSON><PERSON><PERSON>, createError<PERSON>ontext, ConfigurationError, SSMError } from './errorHandler';

/**
 * Configuration interface for all application parameters
 */
export interface AppConfig {
  HUBSPOT_API_KEY: string;
  HUBSPOT_GET_IN_TOUCH_FORM_GUID: string;
  HUBSPOT_AI_READINESS_FORM_GUID: string;
  HUBSPOT_PORTAL_ID: string;
  MAIL_FROM: string;
  MAIL_TO: string;
  SENDGRID_API_KEY: string;
  SENDGRID_CONTACT_US_FORM_TEMPLATE_ID: string;
  SENDGRID_AI_READINESS_FORM_TEMPLATE_ID: string;
  SENDGRID_FAILURE_EMAIL_TEMPLATE_ID: string;
  SLACK_SUCCESS_WEBHOOK_URL: string;
  SLACK_FAILURE_WEBHOOK_URL: string;
  SECOND_RECIPIENT?: string; // Optional parameter
  SITE_URL?: string; // Optional parameter
  STRAPI_URL?: string; // Optional parameter
}

/**
 * SSM Parameter Store configuration service
 * Provides centralized configuration management with caching and fallback support
 */
class SSMConfigService {
  private ssmClient: SSMClient;
  private config: AppConfig | null = null;
  private lastFetchTime: number = 0;
  private readonly CACHE_TTL = 5 * 60 * 1000; // 5 minutes cache
  private readonly PARAMETER_PATH = '/maruti_site/env';
  private isInitialized = false;

  constructor() {
    // Initialize SSM client with default configuration
    // AWS SDK will automatically use environment variables, IAM roles, or AWS config
    this.ssmClient = new SSMClient({
      region: 'ap-south-1'
    });
  }

  /**
   * Initialize the configuration service
   * This should be called once during application startup
   */
  async initialize(): Promise<void> {
    if (this.isInitialized) {
      return;
    }

    const errorHandler = getErrorHandler();
    const context = createErrorContext('initialize', 'SSMConfigService');

    try {
      await this.fetchConfig();
      this.isInitialized = true;
      console.log('✅ SSM Configuration Service initialized successfully');
    } catch (error) {
      // Handle SSM error with fallback to environment variables
      try {
        console.warn('⚠️  SSM initialization failed, attempting environment variable fallback...');
        errorHandler.handleSSMError(error as Error, 'initialize');
        this.loadFromEnvironment();
        this.isInitialized = true;
        console.log('🔄 SSM Configuration Service initialized with environment variable fallback');
      } catch (fallbackError) {
        const configError = new ConfigurationError(
          'Failed to initialize configuration from both SSM and environment variables',
          context
        );
        errorHandler.handleError(configError, context);
        throw configError;
      }
    }
  }

  /**
   * Get the current configuration
   * Automatically refreshes if cache has expired
   */
  async getConfig(): Promise<AppConfig> {
    if (!this.isInitialized) {
      await this.initialize();
    }

    const now = Date.now();
    const cacheExpired = now - this.lastFetchTime > this.CACHE_TTL;

    if (!this.config || cacheExpired) {
      try {
        await this.fetchConfig();
      } catch (error) {
        console.error('Failed to refresh config from SSM, using cached/fallback values:', error);
        // If we have cached config, use it; otherwise load from environment
        if (!this.config) {
          this.loadFromEnvironment();
        }
      }
    }

    if (!this.config) {
      throw new Error('Configuration could not be loaded from SSM or environment variables');
    }

    return this.config;
  }

  /**
   * Fetch configuration from SSM Parameter Store (JSON format)
   */
  private async fetchConfig(): Promise<void> {
    try {
      // Use GetParameter instead of GetParametersByPath since we have a single JSON parameter
      const { GetParameterCommand } = await import('@aws-sdk/client-ssm');
      const command = new GetParameterCommand({
        Name: this.PARAMETER_PATH,
        WithDecryption: true
      });

      const response = await this.ssmClient.send(command);

      if (!response.Parameter || !response.Parameter.Value) {
        throw new Error(`No parameter found at path: ${this.PARAMETER_PATH}`);
      }

      // Parse the JSON configuration
      let jsonConfig: Record<string, string>;
      try {
        jsonConfig = JSON.parse(response.Parameter.Value);
      } catch (parseError) {
        throw new Error(`Failed to parse JSON configuration from SSM parameter: ${parseError}`);
      }

      // Convert JSON config to our AppConfig format
      const ssmConfig: Partial<AppConfig> = {
        HUBSPOT_API_KEY: jsonConfig.NEXT_PUBLIC_HUBSPOT_API_KEY,
        HUBSPOT_GET_IN_TOUCH_FORM_GUID: jsonConfig.NEXT_PUBLIC_HUBSPOT_GET_IN_TOUCH_FORM_GUID,
        HUBSPOT_AI_READINESS_FORM_GUID: jsonConfig.NEXT_PUBLIC_HUBSPOT_AI_READINESS_FORM_GUID || jsonConfig.NEXT_PUBLIC_HUBSPOT_GET_IN_TOUCH_FORM_GUID, // Fallback to contact form GUID if AI readiness GUID is missing
        HUBSPOT_PORTAL_ID: jsonConfig.NEXT_PUBLIC_HUBSPOT_PORTAL_ID,
        MAIL_FROM: jsonConfig.NEXT_PUBLIC_MAIL_FROM,
        MAIL_TO: jsonConfig.NEXT_PUBLIC_MAIL_TO,
        SENDGRID_API_KEY: jsonConfig.NEXT_PUBLIC_SENDGRID_API_KEY,
        SENDGRID_CONTACT_US_FORM_TEMPLATE_ID: jsonConfig.NEXT_PUBLIC_SENDGRID_CONTACT_US_FORM_TEMPLATE_ID,
        SENDGRID_AI_READINESS_FORM_TEMPLATE_ID: jsonConfig.NEXT_PUBLIC_SENDGRID_AI_READINESS_FORM_TEMPLATE_ID || jsonConfig.NEXT_PUBLIC_SENDGRID_CONTACT_US_FORM_TEMPLATE_ID, // Fallback to contact template if AI readiness template is missing
        SENDGRID_FAILURE_EMAIL_TEMPLATE_ID: jsonConfig.NEXT_PUBLIC_SENDGRID_FAILURE_EMAIL_TEMPLATE_ID,
        SLACK_SUCCESS_WEBHOOK_URL: jsonConfig.NEXT_PUBLIC_SLACK_SUCCESS_WEBHOOK_URL,
        SLACK_FAILURE_WEBHOOK_URL: jsonConfig.NEXT_PUBLIC_SLACK_FAILURE_WEBHOOK_URL,
        SECOND_RECIPIENT: jsonConfig.NEXT_PUBLIC_SECOND_RECIPENT, // Note: keeping original typo for compatibility
        // Additional parameters that might be useful
        SITE_URL: jsonConfig.NEXT_PUBLIC_SITE_URL,
        STRAPI_URL: jsonConfig.NEXT_PUBLIC_STRAPI_URL
      };

      // Log any missing parameters for debugging
      const missingFromJson: string[] = [];
      if (!jsonConfig.NEXT_PUBLIC_HUBSPOT_AI_READINESS_FORM_GUID) {
        missingFromJson.push('NEXT_PUBLIC_HUBSPOT_AI_READINESS_FORM_GUID (using contact form GUID as fallback)');
      }
      if (!jsonConfig.NEXT_PUBLIC_SENDGRID_AI_READINESS_FORM_TEMPLATE_ID) {
        missingFromJson.push('NEXT_PUBLIC_SENDGRID_AI_READINESS_FORM_TEMPLATE_ID (using contact template as fallback)');
      }

      if (missingFromJson.length > 0) {
        console.warn('⚠️  Missing parameters in JSON config:', missingFromJson);
      }

      // Validate required parameters and merge with fallbacks
      this.config = this.validateAndMergeConfig(ssmConfig);
      this.lastFetchTime = Date.now();

      console.log(`✅ Successfully loaded configuration from SSM JSON parameter`);
      console.log(`📊 Available parameters: ${Object.keys(jsonConfig).length}`);
    } catch (error) {
      const errorHandler = getErrorHandler();
      const context = createErrorContext('fetchConfig', 'SSMConfigService', {
        parameterPath: this.PARAMETER_PATH,
        configFormat: 'JSON'
      });

      const ssmError = new SSMError(
        `Failed to fetch JSON configuration from SSM parameter: ${this.PARAMETER_PATH}`,
        error as Error,
        context
      );

      errorHandler.handleError(ssmError, context);
      throw ssmError;
    }
  }

  /**
   * Load configuration from environment variables as fallback
   */
  private loadFromEnvironment(): void {
    console.log('Loading configuration from environment variables');

    this.config = this.validateAndMergeConfig({
      HUBSPOT_API_KEY: process.env.NEXT_PUBLIC_HUBSPOT_API_KEY,
      HUBSPOT_GET_IN_TOUCH_FORM_GUID: process.env.NEXT_PUBLIC_HUBSPOT_GET_IN_TOUCH_FORM_GUID,
      HUBSPOT_AI_READINESS_FORM_GUID: process.env.NEXT_PUBLIC_HUBSPOT_AI_READINESS_FORM_GUID,
      HUBSPOT_PORTAL_ID: process.env.NEXT_PUBLIC_HUBSPOT_PORTAL_ID,
      MAIL_FROM: process.env.NEXT_PUBLIC_MAIL_FROM,
      MAIL_TO: process.env.NEXT_PUBLIC_MAIL_TO,
      SENDGRID_API_KEY: process.env.NEXT_PUBLIC_SENDGRID_API_KEY,
      SENDGRID_CONTACT_US_FORM_TEMPLATE_ID: process.env.NEXT_PUBLIC_SENDGRID_CONTACT_US_FORM_TEMPLATE_ID,
      SENDGRID_AI_READINESS_FORM_TEMPLATE_ID: process.env.NEXT_PUBLIC_SENDGRID_AI_READINESS_FORM_TEMPLATE_ID,
      SENDGRID_FAILURE_EMAIL_TEMPLATE_ID: process.env.NEXT_PUBLIC_SENDGRID_FAILURE_EMAIL_TEMPLATE_ID,
      SLACK_SUCCESS_WEBHOOK_URL: process.env.NEXT_PUBLIC_SLACK_SUCCESS_WEBHOOK_URL,
      SLACK_FAILURE_WEBHOOK_URL: process.env.NEXT_PUBLIC_SLACK_FAILURE_WEBHOOK_URL,
      SECOND_RECIPIENT: process.env.NEXT_PUBLIC_SECOND_RECIPENT
    });

    this.lastFetchTime = Date.now();
  }

  /**
   * Validate and merge configuration with fallbacks
   */
  private validateAndMergeConfig(ssmConfig: Partial<AppConfig>): AppConfig {
    const requiredParams = [
      'HUBSPOT_API_KEY',
      'HUBSPOT_GET_IN_TOUCH_FORM_GUID',
      'HUBSPOT_AI_READINESS_FORM_GUID',
      'HUBSPOT_PORTAL_ID',
      'MAIL_FROM',
      'MAIL_TO',
      'SENDGRID_API_KEY',
      'SENDGRID_CONTACT_US_FORM_TEMPLATE_ID',
      'SENDGRID_AI_READINESS_FORM_TEMPLATE_ID',
      'SENDGRID_FAILURE_EMAIL_TEMPLATE_ID',
      'SLACK_SUCCESS_WEBHOOK_URL',
      'SLACK_FAILURE_WEBHOOK_URL'
    ];

    // Parameters that can use fallbacks if missing
    const fallbackParams = [
      'HUBSPOT_AI_READINESS_FORM_GUID', // Can use contact form GUID
      'SENDGRID_AI_READINESS_FORM_TEMPLATE_ID' // Can use contact template
    ];

    const config: Partial<AppConfig> = { ...ssmConfig };
    const missingParams: string[] = [];

    // Check required parameters and provide fallbacks
    for (const param of requiredParams) {
      if (!config[param as keyof AppConfig]) {
        // Check if this parameter can use a fallback value
        let fallbackValue: string | undefined;

        if (fallbackParams.includes(param)) {
          if (param === 'HUBSPOT_AI_READINESS_FORM_GUID') {
            fallbackValue = config.HUBSPOT_GET_IN_TOUCH_FORM_GUID;
          } else if (param === 'SENDGRID_AI_READINESS_FORM_TEMPLATE_ID') {
            fallbackValue = config.SENDGRID_CONTACT_US_FORM_TEMPLATE_ID;
          }
        }

        if (fallbackValue) {
          config[param as keyof AppConfig] = fallbackValue;
          console.log(`🔄 Using fallback value for ${param}`);
        } else {
          // Try to get from environment variable as fallback
          const envKey = `NEXT_PUBLIC_${param}`;
          const envValue = process.env[envKey];

          if (envValue) {
            config[param as keyof AppConfig] = envValue;
            console.log(`🔄 Using environment variable fallback for ${param}`);
          } else {
            missingParams.push(param);
          }
        }
      }
    }

    if (missingParams.length > 0) {
      throw new Error(`Missing required configuration parameters: ${missingParams.join(', ')}`);
    }

    return config as AppConfig;
  }

  /**
   * Force refresh configuration from SSM
   */
  async refreshConfig(): Promise<AppConfig> {
    this.lastFetchTime = 0; // Force cache expiry
    return await this.getConfig();
  }

  /**
   * Get a specific configuration value
   */
  async getConfigValue<K extends keyof AppConfig>(key: K): Promise<AppConfig[K]> {
    const config = await this.getConfig();
    return config[key];
  }

  /**
   * Check if the service is properly initialized
   */
  isServiceInitialized(): boolean {
    return this.isInitialized && this.config !== null;
  }
}

// Create and export a singleton instance
const ssmConfigService = new SSMConfigService();

export default ssmConfigService;

/**
 * Convenience function to get configuration
 * @returns Promise<AppConfig> The application configuration
 */
export const getConfig = async (): Promise<AppConfig> => {
  return await ssmConfigService.getConfig();
};

/**
 * Convenience function to get a specific configuration value
 * @param key The configuration key to retrieve
 * @returns Promise<T> The configuration value
 */
export const getConfigValue = async <K extends keyof AppConfig>(key: K): Promise<AppConfig[K]> => {
  return await ssmConfigService.getConfigValue(key);
};
