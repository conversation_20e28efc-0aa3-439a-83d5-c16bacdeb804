/**
 * Type definitions for the Backend application
 * This file contains all TypeScript interfaces and types used throughout the application
 */

// Re-export configuration types
export { AppConfig } from '../common/ssmConfig';
export { 
  ErrorContext, 
  ErrorDetails, 
  AppError, 
  ConfigurationError, 
  SSMError, 
  ExternalServiceError 
} from '../common/errorHandler';

/**
 * Form data interface for contact-us form submissions
 */
export interface ContactUsFormData {
  firstName?: string;
  lastName?: string;
  emailAddress?: string;
  phoneNumber?: string;
  howDidYouHearAboutUs?: string;
  companyName?: string;
  howCanWeHelpYou?: string;
  utm_source?: string;
  utm_campaign?: string;
  utm_medium?: string;
  clarity?: string;
  url?: string;
  referrer?: string;
  ip_address?: string;
  city?: string;
  country?: string;
  ga_4_userid?: string;
  secondary_source?: string;
  consent?: boolean;
}

/**
 * Form data interface for AI readiness form submissions
 */
export interface AIReadinessFormData extends ContactUsFormData {
  // AI Readiness specific fields
  do_you_have_clearly_defined_business_objectives_and_goals_for_the_ai_project_?: string;
  how_receptive_is_your_leadership_team_to_embracing_the_changes_brought_about_by_ai_?: string;
  do_you_have_budget_allocated_for_your_ai_project_?: string;
  do_you_have_a_robust_data_infrastructure_for_storage__retrieval__and_processing_?: string;
  which_of_the_below_db_tools_do_you_currently_use_?: string;
  is_the_relevant_data_for_the_ai_project_available_and_accessible_?: string;
  do_you_have_access_to_necessary_computing_resources__cpu__gpu__cloud_services__?: string;
  how_would_you_rate_your_organization_s_current_it_infrastructure_in_terms_of_scalability_and_flexib?: string;
  does_the_team_have_the_expertise_in_data_science__machine_learning__and_ai_?: string;
  do_you_have_systems_in_place_to_monitor_the_performance_and_accuracy_of_ai_models_post_deployment_?: string;
  do_you_have_risk_management_strategies_in_place_for_the_ai_project_?: string;
  do_you_have_a_process_in_place_to_measure_the_impact_of_the_deployment_of_ai___ai_powered_solutions?: string;
  
  // Score fields
  strategy___leadership?: string;
  budget___resources?: string;
  talent___skills?: string;
  data_readiness___infrastructure?: string;
  impact_evaliation?: string;
  execution___monitoring?: string;
  average_of_all_score?: string;
}

/**
 * HubSpot form field interface
 */
export interface HubSpotFormField {
  name: string;
  value: string;
}

/**
 * HubSpot payload interface
 */
export interface HubSpotPayload {
  fields: HubSpotFormField[];
  context: {
    pageUri?: string;
  };
  headers: {
    'Content-Type': string;
    Authorization: string;
  };
}

/**
 * Service response interface for consistent API responses
 */
export interface ServiceResponse<T = any> {
  status: number | boolean;
  message?: string;
  error?: string;
  data?: T;
}

/**
 * HubSpot service response
 */
export interface HubSpotResponse extends ServiceResponse {
  status: number;
  message?: string;
  error?: string;
}

/**
 * SendGrid service response
 */
export interface SendGridResponse extends ServiceResponse {
  status: boolean;
  message?: string;
  error?: string;
}

/**
 * Lambda event interface for AWS Lambda handlers
 */
export interface LambdaEvent {
  body: string;
  headers?: Record<string, string>;
  queryStringParameters?: Record<string, string>;
  pathParameters?: Record<string, string>;
  requestContext?: {
    requestId: string;
    identity: {
      sourceIp: string;
      userAgent: string;
    };
  };
}

/**
 * Lambda response interface
 */
export interface LambdaResponse {
  statusCode: number;
  body: string;
  headers?: Record<string, string>;
}

/**
 * SendGrid email data interface
 */
export interface SendGridEmailData {
  from: {
    email: string;
  };
  reply_to: {
    email: string;
  };
  personalizations: Array<{
    to: Array<{
      email: string;
    }>;
    dynamic_template_data: {
      lead: any;
    };
  }>;
  template_id: string;
}

/**
 * Slack message interface
 */
export interface SlackMessage {
  text: string;
  blocks: Array<{
    type: string;
    text?: {
      type: string;
      text: string;
    };
  }>;
}

/**
 * Configuration validation result interface
 */
export interface ValidationResult {
  isValid: boolean;
  missingParameters: string[];
  errors: string[];
  warnings: string[];
}

/**
 * AWS SSM Parameter interface
 */
export interface SSMParameter {
  Name?: string;
  Value?: string;
  Type?: string;
  Version?: number;
}

/**
 * Service configuration interface for external services
 */
export interface ServiceConfig {
  hubspot: {
    apiKey: string;
    portalId: string;
    contactUsFormGuid: string;
    aiReadinessFormGuid: string;
  };
  sendgrid: {
    apiKey: string;
    contactUsTemplateId: string;
    aiReadinessTemplateId: string;
    failureTemplateId: string;
  };
  email: {
    from: string;
    to: string;
    secondRecipient?: string;
  };
  slack: {
    successWebhookUrl: string;
    failureWebhookUrl: string;
  };
}

/**
 * Utility type for making all properties optional
 */
export type Partial<T> = {
  [P in keyof T]?: T[P];
};

/**
 * Utility type for making all properties required
 */
export type Required<T> = {
  [P in keyof T]-?: T[P];
};

/**
 * Utility type for picking specific properties from a type
 */
export type Pick<T, K extends keyof T> = {
  [P in K]: T[P];
};

/**
 * Utility type for omitting specific properties from a type
 */
export type Omit<T, K extends keyof T> = Pick<T, Exclude<keyof T, K>>;

/**
 * Environment variable names enum
 */
export enum EnvironmentVariables {
  HUBSPOT_API_KEY = 'NEXT_PUBLIC_HUBSPOT_API_KEY',
  HUBSPOT_GET_IN_TOUCH_FORM_GUID = 'NEXT_PUBLIC_HUBSPOT_GET_IN_TOUCH_FORM_GUID',
  HUBSPOT_AI_READINESS_FORM_GUID = 'NEXT_PUBLIC_HUBSPOT_AI_READINESS_FORM_GUID',
  HUBSPOT_PORTAL_ID = 'NEXT_PUBLIC_HUBSPOT_PORTAL_ID',
  MAIL_FROM = 'NEXT_PUBLIC_MAIL_FROM',
  MAIL_TO = 'NEXT_PUBLIC_MAIL_TO',
  SENDGRID_API_KEY = 'NEXT_PUBLIC_SENDGRID_API_KEY',
  SENDGRID_CONTACT_US_FORM_TEMPLATE_ID = 'NEXT_PUBLIC_SENDGRID_CONTACT_US_FORM_TEMPLATE_ID',
  SENDGRID_AI_READINESS_FORM_TEMPLATE_ID = 'NEXT_PUBLIC_SENDGRID_AI_READINESS_FORM_TEMPLATE_ID',
  SENDGRID_FAILURE_EMAIL_TEMPLATE_ID = 'NEXT_PUBLIC_SENDGRID_FAILURE_EMAIL_TEMPLATE_ID',
  SLACK_SUCCESS_WEBHOOK_URL = 'NEXT_PUBLIC_SLACK_SUCCESS_WEBHOOK_URL',
  SLACK_FAILURE_WEBHOOK_URL = 'NEXT_PUBLIC_SLACK_FAILURE_WEBHOOK_URL',
  SECOND_RECIPIENT = 'NEXT_PUBLIC_SECOND_RECIPENT'
}

/**
 * SSM Parameter paths enum
 */
export enum SSMParameterPaths {
  HUBSPOT_API_KEY = '/maruti_site/env/NEXT_PUBLIC_HUBSPOT_API_KEY',
  HUBSPOT_GET_IN_TOUCH_FORM_GUID = '/maruti_site/env/NEXT_PUBLIC_HUBSPOT_GET_IN_TOUCH_FORM_GUID',
  HUBSPOT_AI_READINESS_FORM_GUID = '/maruti_site/env/NEXT_PUBLIC_HUBSPOT_AI_READINESS_FORM_GUID',
  HUBSPOT_PORTAL_ID = '/maruti_site/env/NEXT_PUBLIC_HUBSPOT_PORTAL_ID',
  MAIL_FROM = '/maruti_site/env/NEXT_PUBLIC_MAIL_FROM',
  MAIL_TO = '/maruti_site/env/NEXT_PUBLIC_MAIL_TO',
  SENDGRID_API_KEY = '/maruti_site/env/NEXT_PUBLIC_SENDGRID_API_KEY',
  SENDGRID_CONTACT_US_FORM_TEMPLATE_ID = '/maruti_site/env/NEXT_PUBLIC_SENDGRID_CONTACT_US_FORM_TEMPLATE_ID',
  SENDGRID_AI_READINESS_FORM_TEMPLATE_ID = '/maruti_site/env/NEXT_PUBLIC_SENDGRID_AI_READINESS_FORM_TEMPLATE_ID',
  SENDGRID_FAILURE_EMAIL_TEMPLATE_ID = '/maruti_site/env/NEXT_PUBLIC_SENDGRID_FAILURE_EMAIL_TEMPLATE_ID',
  SLACK_SUCCESS_WEBHOOK_URL = '/maruti_site/env/NEXT_PUBLIC_SLACK_SUCCESS_WEBHOOK_URL',
  SLACK_FAILURE_WEBHOOK_URL = '/maruti_site/env/NEXT_PUBLIC_SLACK_FAILURE_WEBHOOK_URL',
  SECOND_RECIPIENT = '/maruti_site/env/NEXT_PUBLIC_SECOND_RECIPIENT'
}
