#!/usr/bin/env node

/**
 * Configuration validation script
 * This script can be run to validate the SSM Parameter Store configuration
 * Usage: node scripts/validateConfig.js
 */

// Register ts-node to handle TypeScript files
require("ts-node/register");

const {
  validateConfiguration,
  getConfigurationSummary,
} = require("../common/validateConfig.ts");

async function main() {
  console.log("🚀 Starting configuration validation...\n");

  try {
    // Run validation
    const validation = await validateConfiguration();

    // Print summary
    const summary = await getConfigurationSummary();
    console.log("\n" + summary);

    // Exit with appropriate code
    if (validation.isValid) {
      console.log("\n🎉 Configuration validation completed successfully!");
      process.exit(0);
    } else {
      console.log("\n💥 Configuration validation failed!");
      console.log(
        "Please check your AWS SSM Parameter Store configuration or environment variables."
      );
      process.exit(1);
    }
  } catch (error) {
    console.error("\n💥 Validation script failed:", error);
    process.exit(1);
  }
}

// Handle unhandled promise rejections
process.on("unhandledRejection", (reason, promise) => {
  console.error("Unhandled Rejection at:", promise, "reason:", reason);
  process.exit(1);
});

// Handle uncaught exceptions
process.on("uncaughtException", (error) => {
  console.error("Uncaught Exception:", error);
  process.exit(1);
});

// Run the main function
main();
