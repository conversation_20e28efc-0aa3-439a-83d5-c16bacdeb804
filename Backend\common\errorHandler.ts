/**
 * Comprehensive error handling utilities for the Backend application
 * Provides centralized error handling, logging, and monitoring capabilities
 */

export interface ErrorContext {
  operation: string;
  component: string;
  userId?: string;
  requestId?: string;
  metadata?: Record<string, any>;
}

export interface ErrorDetails {
  message: string;
  code?: string;
  statusCode?: number;
  originalError?: Error;
  context?: ErrorContext;
  timestamp: Date;
  stack?: string;
}

/**
 * Custom error class for application-specific errors
 */
export class AppError extends Error {
  public readonly code?: string;
  public readonly statusCode: number;
  public readonly context?: ErrorContext;
  public readonly timestamp: Date;
  public readonly isOperational: boolean;

  constructor(
    message: string,
    statusCode: number = 500,
    code?: string,
    context?: ErrorContext,
    isOperational: boolean = true
  ) {
    super(message);
    this.name = 'AppError';
    this.statusCode = statusCode;
    this.code = code;
    this.context = context;
    this.timestamp = new Date();
    this.isOperational = isOperational;

    // Capture stack trace
    Error.captureStackTrace(this, AppError);
  }
}

/**
 * Configuration-specific error class
 */
export class ConfigurationError extends AppError {
  constructor(message: string, context?: ErrorContext) {
    super(message, 500, 'CONFIGURATION_ERROR', context);
    this.name = 'ConfigurationError';
  }
}

/**
 * SSM-specific error class
 */
export class SSMError extends AppError {
  constructor(message: string, originalError?: Error, context?: ErrorContext) {
    super(message, 500, 'SSM_ERROR', context);
    this.name = 'SSMError';
    if (originalError) {
      this.stack = originalError.stack;
    }
  }
}

/**
 * External service error class (HubSpot, SendGrid, Slack)
 */
export class ExternalServiceError extends AppError {
  public readonly service: string;

  constructor(service: string, message: string, statusCode: number = 500, originalError?: Error, context?: ErrorContext) {
    super(`${service} Error: ${message}`, statusCode, 'EXTERNAL_SERVICE_ERROR', context);
    this.name = 'ExternalServiceError';
    this.service = service;
    if (originalError) {
      this.stack = originalError.stack;
    }
  }
}

/**
 * Error handler class with logging and monitoring capabilities
 */
export class ErrorHandler {
  private static instance: ErrorHandler;

  private constructor() {}

  public static getInstance(): ErrorHandler {
    if (!ErrorHandler.instance) {
      ErrorHandler.instance = new ErrorHandler();
    }
    return ErrorHandler.instance;
  }

  /**
   * Handle and log errors with appropriate context
   */
  public handleError(error: Error | AppError, context?: ErrorContext): ErrorDetails {
    const errorDetails: ErrorDetails = {
      message: error.message,
      timestamp: new Date(),
      stack: error.stack
    };

    if (error instanceof AppError) {
      errorDetails.code = error.code;
      errorDetails.statusCode = error.statusCode;
      errorDetails.context = error.context || context;
    } else {
      errorDetails.statusCode = 500;
      errorDetails.originalError = error;
      errorDetails.context = context;
    }

    // Log the error
    this.logError(errorDetails);

    // Send to monitoring service if needed
    this.sendToMonitoring(errorDetails);

    return errorDetails;
  }

  /**
   * Handle SSM-related errors with fallback logic
   */
  public handleSSMError(error: Error, operation: string, fallbackValue?: any): any {
    const context: ErrorContext = {
      operation,
      component: 'SSM',
      metadata: { fallbackAvailable: !!fallbackValue }
    };

    const ssmError = new SSMError(`SSM operation failed: ${operation}`, error, context);
    const errorDetails = this.handleError(ssmError, context);

    // Log fallback usage
    if (fallbackValue !== undefined) {
      console.warn(`🔄 Using fallback value for SSM operation: ${operation}`);
      return fallbackValue;
    }

    throw ssmError;
  }

  /**
   * Handle external service errors with retry logic information
   */
  public handleExternalServiceError(
    service: string,
    error: Error,
    operation: string,
    statusCode?: number,
    retryAttempt?: number
  ): ExternalServiceError {
    const context: ErrorContext = {
      operation,
      component: service,
      metadata: { 
        statusCode,
        retryAttempt,
        isRetryable: this.isRetryableError(statusCode)
      }
    };

    const serviceError = new ExternalServiceError(service, error.message, statusCode, error, context);
    this.handleError(serviceError, context);

    return serviceError;
  }

  /**
   * Log errors with appropriate level and formatting
   */
  private logError(errorDetails: ErrorDetails): void {
    const logLevel = this.getLogLevel(errorDetails.statusCode);
    const logMessage = this.formatErrorMessage(errorDetails);

    switch (logLevel) {
      case 'error':
        console.error(logMessage);
        break;
      case 'warn':
        console.warn(logMessage);
        break;
      case 'info':
        console.info(logMessage);
        break;
      default:
        console.log(logMessage);
    }

    // Log stack trace for server errors
    if (errorDetails.statusCode && errorDetails.statusCode >= 500 && errorDetails.stack) {
      console.error('Stack trace:', errorDetails.stack);
    }
  }

  /**
   * Format error message for logging
   */
  private formatErrorMessage(errorDetails: ErrorDetails): string {
    const timestamp = errorDetails.timestamp.toISOString();
    const context = errorDetails.context;
    
    let message = `[${timestamp}] ${errorDetails.message}`;
    
    if (errorDetails.code) {
      message += ` (Code: ${errorDetails.code})`;
    }
    
    if (errorDetails.statusCode) {
      message += ` (Status: ${errorDetails.statusCode})`;
    }
    
    if (context) {
      message += ` [${context.component}:${context.operation}]`;
      
      if (context.requestId) {
        message += ` (Request: ${context.requestId})`;
      }
      
      if (context.metadata) {
        message += ` Metadata: ${JSON.stringify(context.metadata)}`;
      }
    }
    
    return message;
  }

  /**
   * Determine log level based on status code
   */
  private getLogLevel(statusCode?: number): string {
    if (!statusCode) return 'error';
    
    if (statusCode >= 500) return 'error';
    if (statusCode >= 400) return 'warn';
    if (statusCode >= 300) return 'info';
    return 'info';
  }

  /**
   * Check if an error is retryable based on status code
   */
  private isRetryableError(statusCode?: number): boolean {
    if (!statusCode) return false;
    
    // Retry on server errors and specific client errors
    const retryableCodes = [429, 500, 502, 503, 504];
    return retryableCodes.includes(statusCode);
  }

  /**
   * Send error details to monitoring service (placeholder for future implementation)
   */
  private sendToMonitoring(errorDetails: ErrorDetails): void {
    // TODO: Implement monitoring service integration (e.g., CloudWatch, Datadog, etc.)
    // For now, we'll just log that monitoring would be triggered
    if (errorDetails.statusCode && errorDetails.statusCode >= 500) {
      console.log(`📊 [MONITORING] Critical error detected: ${errorDetails.code || 'UNKNOWN_ERROR'}`);
    }
  }
}

/**
 * Convenience function to get the error handler instance
 */
export const getErrorHandler = (): ErrorHandler => ErrorHandler.getInstance();

/**
 * Utility function to safely execute async operations with error handling
 */
export async function safeExecute<T>(
  operation: () => Promise<T>,
  context: ErrorContext,
  fallbackValue?: T
): Promise<T> {
  const errorHandler = getErrorHandler();
  
  try {
    return await operation();
  } catch (error) {
    if (fallbackValue !== undefined) {
      errorHandler.handleError(error as Error, context);
      console.warn(`🔄 Using fallback value for operation: ${context.operation}`);
      return fallbackValue;
    }
    
    throw errorHandler.handleError(error as Error, context);
  }
}

/**
 * Utility function to create error context
 */
export function createErrorContext(
  operation: string,
  component: string,
  metadata?: Record<string, any>
): ErrorContext {
  return {
    operation,
    component,
    metadata
  };
}
